import Link from 'next/link';
import styles from './page.module.css';

export default function Home() {
  return (
    <div className={styles.main}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.userInfo}>
            <div className={styles.avatar}>
              <img src="https://ui-avatars.com/api/?background=random&name=Sarah" alt="Avatar" />
            </div>
            <div className={styles.greeting}>
              <h3>Hi,<PERSON></h3>
              <p>Welcome back</p>
            </div>
          </div>
          <div><Link href="/test">Test</Link></div>
          <div className={styles.notificationIcon}>
            <span>🔔</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={styles.content}>
        <div className={styles.container}>

          {/* Hero Section */}
          <section className={styles.heroSection}>
            <h1>Explore new recipes</h1>

            {/* Search Bar */}
            <div className={styles.searchContainer}>
              <div className={styles.searchBar}>
                <span className={styles.searchIcon}>🔍</span>
                <input
                  type="text"
                  placeholder="Search recipes"
                  className={styles.searchInput}
                />
                <button className={styles.filterBtn}>⚙️</button>
              </div>
            </div>
          </section>

          {/* Categories */}
          <section className={styles.categoriesSection}>
            <div className={styles.categories}>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍔</div>
                <span>Sandwich</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🥗</div>
                <span>Salad</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍕</div>
                <span>Pizza</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍝</div>
                <span>Spaghetti</span>
              </div>
            </div>
          </section>

          {/* Featured Recipe - Mix Salad Avocado */}
          <section className={styles.featuredSection}>
            <div className={styles.featuredCard} style={{background: '#E8F5E9'}}>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder} style={{background: 'linear-gradient(135deg, #C8E6C9 0%, #A5D6A7 100%)'}}>
                  <img src="/salad-avocado.jpg" alt="Mix Salad Avocado" style={{width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px'}} />
                </div>
                <button className={styles.favoriteBtn}>🖤</button>
              </div>
              <div className={styles.courseInfo}>
                <h3>Mix Salad Avocado</h3>
                <button className={styles.seeRecipeBtn} style={{background: '#81C784', color: '#fff'}}>See recipe →</button>
              </div>
            </div>
          </section>

          {/* Popular Recipe - Mix Salad Chicken */}
          <section className={styles.popularSection}>
            <div className={styles.popularCard} style={{background: '#E3F2FD'}}>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder} style={{background: 'linear-gradient(135deg, #BBDEFB 0%, #90CAF9 100%)'}}>
                  <img src="/salad-chicken.jpg" alt="Mix Salad Chicken" style={{width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px'}} />
                </div>
                <button className={styles.favoriteBtn}>🖤</button>
              </div>
              <div className={styles.courseInfo}>
                <h3>Mix Salad Chicken</h3>
              </div>
            </div>
          </section>

          {/* Recipe Detail Section */}
          <section className={styles.recipeDetailSection}>
            <div className={styles.recipeDetailCard}>
              <div className={styles.recipeHeader}>
                <button className={styles.backBtn}>←</button>
                <h2>Recipe</h2>
                <button className={styles.moreBtn}>⋯</button>
              </div>
              
              <div className={styles.recipeImage}>
                <img src="/salad-detail.jpg" alt="Mix Salad Chicken" style={{width: '100%', height: '240px', objectFit: 'cover', borderRadius: '20px'}} />
              </div>

              <h3 className={styles.recipeTitle}>Mix Salad Chicken</h3>
              
              <div className={styles.nutritionInfo}>
                <div className={styles.nutritionItem}>
                  <span className={styles.nutritionValue}>520</span>
                  <span className={styles.nutritionLabel}>Kcal</span>
                </div>
                <div className={styles.nutritionItem}>
                  <span className={styles.nutritionValue}>320</span>
                  <span className={styles.nutritionLabel}>grams</span>
                </div>
                <div className={styles.nutritionItem}>
                  <span className={styles.nutritionValue}>25</span>
                  <span className={styles.nutritionLabel}>min</span>
                </div>
                <div className={styles.nutritionItem}>
                  <span className={styles.nutritionValue}>2</span>
                  <span className={styles.nutritionLabel}>serve</span>
                </div>
              </div>

              <div className={styles.recipeTabs}>
                <button className={styles.tabBtn} style={{background: '#FFE0E6', color: '#E91E63'}}>Lunch</button>
                <button className={styles.tabBtn} style={{background: '#FCE4EC', color: '#EC407A'}}>Salad</button>
                <button className={styles.tabBtn} style={{background: '#E3F2FD', color: '#42A5F5'}}>Health</button>
              </div>

              <div className={styles.ingredientsSection}>
                <h4>Ingredients</h4>
                <p className={styles.ingredientsCount}>6 Healthy ingredients</p>
              </div>
            </div>
          </section>

          {/* Weekly Report Section */}
          <section className={styles.reportSection}>
            <div className={styles.reportCard}>
              <div className={styles.reportHeader}>
                <div className={styles.userInfo}>
                  <div className={styles.avatar}>
                    <img src="https://ui-avatars.com/api/?background=random&name=Sarah" alt="Avatar" />
                  </div>
                  <div className={styles.greeting}>
                    <h3>Hi,Sarah</h3>
                    <p>Welcome back</p>
                  </div>
                </div>
                <div className={styles.notificationIcon}>
                  <span>🔔</span>
                </div>
              </div>

              <h2>Report <span className={styles.reportPeriod}>on this week</span></h2>
              <p className={styles.dateRange}>from 16-22 February 2025</p>

              <div className={styles.nutritionChart}>
                <div className={styles.chartCircle}>
                  <span className={styles.daysCount}>7 days</span>
                </div>
                <div className={styles.chartLegend}>
                  <div className={styles.legendItem}>
                    <span className={styles.legendDot} style={{background: '#81C784'}}></span>
                    <span>Calories</span>
                    <span className={styles.legendValue}>1925kCal</span>
                  </div>
                  <div className={styles.legendItem}>
                    <span className={styles.legendDot} style={{background: '#64B5F6'}}></span>
                    <span>Carbohydrates</span>
                    <span className={styles.legendValue}>250g</span>
                  </div>
                  <div className={styles.legendItem}>
                    <span className={styles.legendDot} style={{background: '#FFB74D'}}></span>
                    <span>Protein</span>
                    <span className={styles.legendValue}>68g</span>
                  </div>
                  <div className={styles.legendItem}>
                    <span className={styles.legendDot} style={{background: '#BA68C8'}}></span>
                    <span>Fats</span>
                    <span className={styles.legendValue}>263g</span>
                  </div>
                </div>
              </div>

              <h3 className={styles.myRecipeTitle}>My Recipe</h3>
              <div className={styles.myRecipeCard} style={{background: '#E8F5E9'}}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder} style={{background: 'linear-gradient(135deg, #C8E6C9 0%, #A5D6A7 100%)'}}>
                    <img src="/salad-avocado.jpg" alt="Mix Salad Avocado" style={{width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px'}} />
                  </div>
                  <button className={styles.favoriteBtn}>🖤</button>
                </div>
                <div className={styles.courseInfo}>
                  <h3>Mix Salad Avocado</h3>
                </div>
              </div>
            </div>
          </section>

        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className={styles.bottomNav}>
        <div className={`${styles.navItem} ${styles.active}`}>
          <span className={styles.navIcon}>🏠</span>
          <span className={styles.navLabel}>Home</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>⏰</span>
          <span className={styles.navLabel}>Time</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>📊</span>
          <span className={styles.navLabel}>Statistic</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>👤</span>
          <span className={styles.navLabel}>Profile</span>
        </div>
      </nav>
    </div>
  );
}
