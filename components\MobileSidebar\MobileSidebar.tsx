'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import styles from './MobileSidebar.module.css';

interface MobileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath?: string;
}

const navigationItems = [
  { href: '/', icon: '🏠', label: 'Home' },
  { href: '/courses', icon: '📚', label: 'Browse Courses' },
  { href: '/my-courses', icon: '🎓', label: 'My Courses' },
  { href: '/certificates', icon: '🏆', label: 'Certificates' },
  { href: '/profile', icon: '👤', label: 'Profile' },
  { href: '/settings', icon: '⚙️', label: 'Settings' },
  { href: '/test', icon: '🧪', label: 'Test' },
];

export default function MobileSidebar({ isOpen, onClose, currentPath = '/' }: MobileSidebarProps) {
  // Close sidebar with Escape key and prevent body scroll
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    // Prevent body scroll when sidebar is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* Mobile Sidebar Overlay */}
      {isOpen && (
        <div className={`${styles.sidebarOverlay} ${styles.show}`} onClick={onClose}></div>
      )}

      {/* Mobile Sidebar */}
      <div className={`${styles.mobileSidebar} ${isOpen ? styles.sidebarOpen : ''}`}>
        <div className={styles.sidebarHeader}>
          <h2 className={styles.sidebarTitle}>CourseAssist</h2>
          <button className={styles.closeSidebarBtn} onClick={onClose}>
            ✕
          </button>
        </div>
        
        <nav className={styles.sidebarNav}>
          {navigationItems.map((item) => (
            <Link 
              key={item.href}
              href={item.href} 
              className={`${styles.sidebarNavItem} ${currentPath === item.href ? styles.active : ''}`}
              onClick={onClose}
            >
              <span className={styles.sidebarNavIcon}>{item.icon}</span>
              <span className={styles.sidebarNavLabel}>{item.label}</span>
            </Link>
          ))}
        </nav>
        
        <div className={styles.sidebarFooter}>
          <button className={styles.logoutBtn} onClick={onClose}>
            <span className={styles.sidebarNavIcon}>🚪</span>
            <span className={styles.sidebarNavLabel}>Logout</span>
          </button>
        </div>
      </div>
    </>
  );
}
