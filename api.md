# Updated Mobile App Feature Documentation (Based on Requirements + API Documentation)

Below is a detailed breakdown of page structure, interaction flows, essential APIs, and key details for each page—incorporating your requested modifications (Order page replacing Statistics; Home page centralizing all activities; Profile expanding to include announcements/customer service/wallet/invitations; supporting order placement without login).

---

## Bottom Navigation (Global)

Fixed 4 menus (at bottom of all pages):

- Home (Homepage — Activities/Recommendations)
- Explore / Categories (Categories/Search)
- Orders (Order Progress Page) ← Replacing original Statistics page
- Profile (My Account)

Click to switch corresponding routes while maintaining page cache/state.

---

## 1. Homepage — "All Activities" Central Page (Home)

**Objective**: Centralize all marketing/promotions/activities/flash sales/VIP features on homepage to drive conversions.

### Modules & APIs

- **Top Welcome + Search**

  - `GET /api/v1/users/profile` (displays nickname/avatar/balance etc., shows guest if not logged in)
  - Search: `GET /api/v1/public/courses?keyword=...`

- **Carousel/Activity Entry** (prominent position)

  - `GET /api/v1/public/banners?position=home` (links to courses/activities or external links)

- **Activity Card Collection (Required)**

  - **Flash Sales**: `GET /api/v1/public/flash-sales` (displays sale courses, countdown, buy now)
  - **Membership Package Recommendations**: `GET /api/v1/public/membership-packages` (entry to purchase)
  - **Coupon/Gift Card Entry**: Input/validate `GET /api/v1/public/coupons/{code}/validate`, `GET /api/v1/public/gift-cards/{code}/validate`
  - **Featured/Activity Collection** (customizable): Call backend custom activity API or carousel links

- **Recommended Courses / Popular Categories**

  - `GET /api/v1/public/categories` (categories)
  - `GET /api/v1/public/courses?sort=sales_desc&page=1&page_size=...` (recommended/bestsellers)

### Interaction Details

- Activity card click → Course details or activity details page
- Flash sale button: If user not logged in, encourage "Quick Order Without Login" or one-click login (see Guest Checkout section)
- All activity cards should display remaining stock/time/tags (e.g., "Limited Time", "Bestseller", "Member Price")

---

## 2. Course Detail Page (Product Detail Page) — Including **Order Button + Modal**

**Objective**: Display course/product details and complete order (supports guest checkout).

### Page Content & APIs

- Basic Info: `GET /api/v1/public/courses/{id}` (includes `enable_image_search`, `image_search_platform`, image URLs etc.)
- Image carousel, course description, price, sales volume, platform information

### Fixed Bottom: **Order Button (Order Now)**

- Click opens **Order Modal** (no page navigation)

#### Order Modal Fields (Your Three Required Items)

- Phone Number / Account (Required) → `student_account`
- Password (Required) → `student_password`
- School Name (Optional) → `school_name`

Modal contains two types of buttons:

1. **Query Course** (below input fields)

   - **Prerequisite**: Course `enable_image_search == true` (from course detail API)
   - **Frontend Request (Recommended Backend Proxy API)**: Suggested frontend call (example)

     ```
     POST /api/v1/public/courses/{id}/image-search
     body: { school: "...", user: "student ID/account", pass: "password" }
     ```

     > Note: Original image search implementation is admin API (`POST /api/v1/admin/platforms/image-search`), this action should be proxied/orchestrated by backend (due to platform account sensitivity and possible admin key requirements). Frontend uses above "public" proxy API for backend to complete actual course search and return results.

   - **Expected Response (Example)**:

     ```json
     {
       "code": 1,
       "msg": "Query successful",
       "userName": "Zhang San",
       "data": [
         {
           "name": "Course A",
           "imageurl": "https://.../a.jpg",
           "state": "In Progress",
           "teachername": "Teacher Li",
           "id": "CRS123"
         }
       ]
     }
     ```

   - **UI Display**: Show query results list in modal (course image + name + status + select button). If no results, provide error/hint (e.g., "Not found, please verify account or choose another platform").
   - **Error Handling**: Display backend error messages (e.g., INVALID_CREDENTIALS, SERVICE_UNAVAILABLE etc.).

2. **Confirm Order (Create Order)**

   - If course queried and selected → Can directly submit order with queried course info (easier auto-fill)
   - **Request**: `POST /api/v1/orders/` (requires JWT or temporary token for guest checkout)

     ```json
     {
       "course_id": 1, // Optional, if course ID obtained from query
       "order_type": "course",
       "school_name": "...",
       "student_account": "...",
       "student_password": "...",
       "customer_email": "...", // Optional
       "notes": "..."
     }
     ```

   - **Response**: Returns order information (order id / order_sn / status etc.), then guides to payment or shows order detail page.
   - **Payment**: Call `POST /api/v1/orders/{id}/pay` or `POST /api/v1/payments/`.

### Security & Privacy

- Student ID/password should be submitted via HTTPS; frontend shouldn't store password plaintext (use only in current request or temporary local cache with security risk notice).
- Course query may involve third-party account login (platform), recommend backend handling of retry/rate limiting/auditing.

---

## 3. Orders Page (Replacing Statistics)

**Objective**: Centrally display all user orders and real-time progress for each order.

### Page Content & APIs

- **Order List**: `GET /api/v1/users/orders?page=...`

  - List fields: Order number, course name, amount, payment status, processing status (pending/processing/completed/failed), creation time

- **Order Details**: `GET /api/v1/orders/{id}` (includes `progress`, `platform_order_id`, `is_submit`, `submit_time` etc.)
- **Cancel Order**: `PUT /api/v1/orders/{id}/cancel`
- **Pay/Repay**: `POST /api/v1/orders/{id}/pay` or `POST /api/v1/payments/`
- **Copy Order Number / Contact Support / Track Status** (UI operations)

### Real-time Updates (Recommended)

- Use WebSocket: `ws://api.example.com/ws?token={jwt}` subscribe to `order_status` channel to receive `order_update` messages → Update order progress bar.
- Backend notifications also sent via email/SMS (if provided) (see Notification System).

### UI Interaction (Key Points)

- Each order supports: View details, pay, cancel, contact support, copy order number, view processing log (if available).
- Status displayed with progress bar + text (e.g., Created → Paid → Submitted to Platform → Processing → Completed), with timestamps.
- For guest orders, provide **order query code/email** for subsequent order lookup.

---

## 4. Profile — Enhanced Features Page

**Objective**: Personal info + all self-service entries (announcements, support, wallet, invites, membership, orders, settings).

### Core Modules & API Mapping

- **User Info Card**: `GET /api/v1/users/profile` (displays nickname, faceimg, balance, vip_status, query_count etc.)
- **Announcement List**: `GET /api/v1/public/announcements` (click for details `GET /api/v1/public/announcements/{id}`)
- **Customer Service**: `GET /api/v1/public/customer-service` (shows QQ/WeChat/phone/work hours, provides one-click call/copy/online chat entry)
- **Wallet / Balance**:

  - Display balance: from `GET /api/v1/users/profile` `balance` field
  - Recharge/payment records: `GET /api/v1/users/payments`, create payment via `POST /api/v1/payments/`

- **Invites / Invite Codes**:

  - Generate invite code: `GET /api/v1/promotions/invite/generate-code`
  - Invite statistics: `GET /api/v1/promotions/invite/stats`

- **Membership Center**: `GET /api/v1/public/membership-packages` + Purchase: `POST /api/v1/membership/purchase`
- **Coupon/Gift Card Entry** (Redemption/Validation): `GET /api/v1/public/coupons/{code}/validate`, `GET /api/v1/public/gift-cards/{code}/validate`
- **My Orders Entry**: Navigate to Orders page (same as Orders page)
- **Settings/Logout**: `POST /api/v1/auth/logout`; Update profile `PUT /api/v1/users/profile`

### Interaction & UX Tips

- Announcements show "unread/read" status and allow important announcements to be pinned (backend config `is_popup` controls first-screen popup)
- Customer service button permanent, prominently suggests "Contact Support" during order issues (payment failure/order stuck)
- Wallet/recharge flow needs clear indication of transaction records and refund process

---

## 5. Guest Checkout (Must Support) — Implementation Suggestions & Frontend Flow

Documentation provides multiple approaches, frontend/backend collaboration suggestions as follows (UX priority):

### Recommended Implementation (Hybrid)

1. **Quick Guest Order (Default)**

   - Frontend on first order initiation: Call backend to create temporary guest account (`POST /api/v1/auth/register`) and auto-login (`POST /api/v1/auth/login`) to get token, or backend directly returns temporary token.
   - Use token to call `POST /api/v1/orders/` to create order.
   - Advantage: Orders and users traceable, can upgrade later.

2. **One-Click Login (If User Willing)**

   - Guide to use `GET /api/v1/auth/oauth/login/{type}` (e.g., WeChat/QQ) for one-click login, enabling higher privilege operations.

3. **Order Query Code (Easy Order Lookup)**

   - Generate query code after order creation and return to user via interface/SMS/email. Can check order status with query code without login (backend needs to implement public query API).

### Frontend UX Flow (During Order)

- User clicks "Order Now" → Modal opens for account/password/school input → Optional "Query Course" (if course supports) → Select/Confirm → Click "Order"
- If not logged in: Show "Quick Order (No Login Required)" explanation and proceed with temporary guest creation or suggest one-click login
- After order success: Show order page/payment page/query code

---

## 6. Image Course Search (About `enable_image_search`)

- Course detail API includes `enable_image_search` (bool) and `image_search_platform` (string). Frontend should determine whether to show "Query Course" button based on this.
- **Important Security Note**: Current API doc implements image search as admin API (`POST /api/v1/admin/platforms/image-search`), so frontend should not call admin API directly. **Recommended Implementation**: Backend provides public proxy API (example `POST /api/v1/public/courses/{id}/image-search`), internally called by backend service and returns results to frontend. Frontend only calls this secure proxy API.

---

## 7. Notifications / Real-time & Fault Tolerance

- Order/payment updates: Supports WebSocket `order_status` channel, frontend subscribes for real-time order progress updates.
- Important events (order/payment success/order completion) trigger backend email/SMS (if `customer_email` provided) and system notifications (`GET /api/v1/public/announcements/popup` available for popups).
- Rate limiting & anti-abuse: Frontend should implement anti-repeat and throttling on key buttons (query course, create order, payment), display friendly loading and failure retry prompts.

---

Alright, I've helped integrate the "Query Course → Select Course → Order Details Page (Payment Method + Notification Method)" flow into the updated documentation, refining the order section into final interaction specifications.

---

# 📱 Mobile Feature Documentation (Updated — Including Notification Method Selection)

## **Order Flow Overview**

1. User clicks [Order Now] on **Course Detail Page**
2. **Order Modal** pops up → Fill account info (phone/account, password, optional school name)
3. Click [Query Course] (if course supports image search)
4. **Select one course** from returned course list (checkbox/radio card)
5. Click [Next] to enter **Order Details Page** (confirm info + payment method + notification method)
6. Enter notification email/phone (optional)
7. Select payment method and pay

---

## **Course Detail Page (Order Button + Modal)**

### Modal Fields

- Phone/Account (Required) → `student_account`
- Password (Required) → `student_password`
- School Name (Optional) → `school_name`

### Modal Buttons

1. **Query Course**

   - Condition: `enable_image_search == true`
   - Call backend secure proxy API (backend proxies `/api/v1/admin/platforms/image-search`)
   - Results shown as **selectable list** (card format: image + name + status + instructor)
   - User must select one course to enable [Next]

2. **Next** (only available when course selected)

   - Navigate to **Order Details Page**

---

## **Order Details Page (New Page)**

> This is the page user enters after selecting course, for order confirmation, payment method selection, and optional notification contact info.

### Page Structure

1. **Order Information Confirmation Area**

   - Course image, course name (from query results or course details)
   - Student ID/account (masked display), school name
   - Price information (original price / VIP price / final price)

2. **Notification Method Input Area** (optional but encouraged)

   - **Notification Email** (corresponds to `customer_email` parameter)
   - **Notification Phone** (if backend supports SMS, can be extension field `customer_phone`)
   - Prompt:

     > "Provide email or phone to receive notifications when order completes (order status, study account etc.)"

3. **Payment Method Selection Area**

   - Get payment methods API: `GET /api/v1/payments/methods`
   - Supports: Alipay / WeChat / Balance payment
   - After selection call:

     - First create order: `POST /api/v1/orders/`
     - Then create payment: `POST /api/v1/payments/`

4. **Confirm Payment Button**

   - Click navigates to payment page or calls payment SDK (H5 payment)

---

## **Order + Payment API Flow**

```plaintext
[Course Detail Page]
  ↓ Click Order Now
[Order Modal]
  ↓ Input account info + Query course
  ↓ Select course
[Order Details Page]
  ↓ Enter notification method + Select payment method
  ↓ POST /api/v1/orders/  (including customer_email / customer_phone)
  ↓ POST /api/v1/payments/
  ↓ Navigate to payment
```

---

## **Post-Payment Processing**

- Frontend monitors WebSocket `payment_status` or polls `GET /api/v1/payments/status/{order_sn}`
- After payment success → Navigate to order details (progress page) or prompt "We'll notify you of course account info via email/SMS"
- If notification method provided, backend pushes order completion notification via email/SMS (see documentation notification system description)
