# User Self-Service Order System Complete API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Authentication Related APIs](#1-authentication-related-apis)
3. [Course Query APIs](#2-course-query-apis)
4. [Order Related APIs](#3-order-related-apis)
5. [Payment Related APIs](#4-payment-related-apis)
6. [Personal Center APIs](#5-personal-center-apis)
7. [Membership Package APIs](#6-membership-package-apis)
8. [Promotion APIs](#7-promotion-apis)
9. [Notification APIs](#8-notification-apis)
10. [Image Related APIs](#9-image-related-apis)
11. [Image Course Query APIs](#10-image-course-query-apis)
12. [System Configuration APIs](#11-system-configuration-apis)
13. [Notification System Description](#12-notification-system-description)
14. [Order Without Login Description](#13-order-without-login-description)
15. [Static Resource Access](#14-static-resource-access)
16. [Test APIs](#15-test-apis)

## Overview

### Basic Information

- **API Base Path**: `/api/v1`
- **Protocol**: HTTP/HTTPS
- **Data Format**: JSON
- **Authentication Method**: JWT Token
- **Character Encoding**: UTF-8
- **Static Resource Access**: `/uploads` (Public access path for uploaded resources like images, files)

### Common Request Headers

```http
Content-Type: application/json
Authorization: Bearer {token}  # Required for authenticated endpoints
```

### Common Response Format

Success Response:

```json
{
  "data": {}, // Response data
  "message": "Operation successful",
  "code": 200
}
```

Error Response:

```json
{
  "error": "Error description",
  "code": 400
}
```

### HTTP Status Codes

| Status Code | Description                        |
| ----------- | ---------------------------------- |
| 200         | Success                            |
| 201         | Created Successfully               |
| 400         | Bad Request                        |
| 401         | Unauthorized/Authentication Failed |
| 403         | Forbidden                          |
| 404         | Resource Not Found                 |
| 500         | Internal Server Error              |

---

## 1. Authentication Related APIs

### 1.1 User Login (Traditional Method)

**POST** `/api/v1/auth/login`

No authentication required

**Request Parameters:**

```json
{
  "username": "<EMAIL>", // Required, username or email
  "password": "password123" // Required, password
}
```

**Response Example:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "username": "<EMAIL>",
    "role": "user",
    "nickname": "John Smith",
    "faceimg": "/uploads/avatars/user1.jpg",
    "balance": 100.5,
    "vip_status": true,
    "vip_expire_at": "2025-12-31T23:59:59Z",
    "membership_type": "vip",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 User Registration

**POST** `/api/v1/auth/register`

No authentication required

**Request Parameters:**

```json
{
  "username": "<EMAIL>", // Required, username or email
  "password": "password123", // Required, password
  "role": "user" // Optional, default is "user", options: user/admin/operator
}
```

**Response Example:**

```json
{
  "message": "User created successfully",
  "user_id": 2
}
```

### 1.3 Refresh Token

**POST** `/api/v1/auth/refresh`

Authentication required (provide current token in Authorization header)

**Response Example:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 1.4 Logout

**POST** `/api/v1/auth/logout`

Authentication required

**Response Example:**

```json
{
  "message": "Logged out successfully"
}
```

### 1.5 Get Supported OAuth Login Types (One-Click Login)

**GET** `/api/v1/auth/oauth/types`

No authentication required

**Response Example:**

```json
{
  "data": {
    "qq": "QQ",
    "wx": "WeChat",
    "alipay": "Alipay",
    "sina": "Weibo",
    "baidu": "Baidu",
    "douyin": "TikTok",
    "huawei": "Huawei",
    "xiaomi": "Xiaomi",
    "google": "Google",
    "microsoft": "Microsoft",
    "facebook": "Facebook",
    "twitter": "Twitter",
    "wework": "WeWork",
    "dingtalk": "DingTalk",
    "gitee": "Gitee",
    "github": "GitHub"
  }
}
```

### 1.6 Get OAuth Login URL (One-Click Login)

**GET** `/api/v1/auth/oauth/login/{type}`

No authentication required

**Path Parameters:**

- `type`: Login type, see supported values in types list above (qq/wx/alipay/sina/baidu/douyin etc.)

**Response Example:**

```json
{
  "login_url": "https://u.cccyun.cc/connect.php?act=login&appid=1000&type=qq&redirect_uri=...",
  "qr_code": "data:image/png;base64,...", // QR code for platforms that support it
  "type": "qq"
}
```

**Note:**

- System uses aggregated login service, supporting 16 mainstream third-party platform one-click logins
- Users will be redirected to the respective platform's authorization page after clicking the login URL
- After successful authorization, will automatically redirect back to system callback URL

### 1.7 OAuth Login Callback

**GET** `/api/v1/auth/oauth/callback`

No authentication required (called by third-party platform)

**Query Parameters:**

- `code`: Authorization code
- `type`: Login type
- `state`: State parameter (optional)

**Response Example:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 3,
    "username": "qq_12345678", // Format: platform_type_social_uid first 8 digits
    "role": "user",
    "social_uid": "1234567890",
    "login_type": "qq",
    "nickname": "QQ User",
    "faceimg": "http://q.qlogo.cn/...",
    "gender": "male",
    "location": "New York",
    "is_active": true,
    "balance": 0.0,
    "created_at": "2025-01-15T10:00:00Z"
  }
}
```

**Note:**

- First-time third-party login will automatically create new user
- Username is automatically generated in format: platform_type_social_uid first 8 digits
- If user with same social_uid exists, directly login and update user info
- Third-party login users don't need to set password

### 1.8 Forgot Password (Email Service Configuration Required)

**POST** `/api/v1/auth/forgot-password`

No authentication required

**Request Parameters:**

```json
{
  "email": "<EMAIL>" // Required, registered email
}
```

**Response Example:**

```json
{
  "message": "Password reset link sent to your email"
}
```

### 1.9 Change Password

**POST** `/api/v1/users/change-password`

Authentication required

**Request Parameters:**

```json
{
  "current_password": "oldpassword123", // Required, current password
  "new_password": "newpassword456" // Required, new password
}
```

**Response Example:**

```json
{
  "message": "Password changed successfully"
}
```

### 1.10 Reset Password (Forgot Password Process)

**POST** `/api/v1/auth/reset-password`

No authentication required

**Request Parameters:**

```json
{
  "token": "reset_token_from_email", // Required, reset token from email
  "new_password": "newpassword456" // Required, new password
}
```

**Response Example:**

```json
{
  "message": "Password reset successful"
}
```

---

## 2. Course Query APIs

### 2.1 Get Public Course List

**GET** `/api/v1/public/courses`

No authentication required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| category_id | int | No | Category ID |
| min_price | float | No | Minimum price |
| max_price | float | No | Maximum price |
| keyword | string | No | Search keyword |
| sort | string | No | Sort method: price_asc/price_desc/sales_desc/name_asc/created_at_desc(default) |
| page | int | No | Page number, default 1 |
| page_size | int | No | Items per page, default 12, max 100 |

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "name": "Python Programming Basics",
      "description": "Learn Python from scratch",
      "price": 99.0,
      "vip_price": 79.0,
      "super_vip_price": 59.0,
      "img": "/uploads/courses/python.jpg",
      "image_url": "/uploads/courses/python.jpg",
      "sales": 1520,
      "status": "1",
      "category": {
        "id": 1,
        "name": "Programming Development",
        "description": "Programming development courses"
      },
      "interface_platform": {
        "id": 1,
        "platform_name": "Learning Platform A",
        "name": "2xx"
      },
      "enable_image_search": true,
      "image_search_platform": "zykck",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 12,
    "total": 100,
    "total_pages": 9
  }
}
```

### 2.2 Get Course Details

**GET** `/api/v1/public/courses/{id}`

No authentication required

**Path Parameters:**

- `id`: Course ID

**Response Example:**

```json
{
  "id": 1,
  "name": "Python Programming Basics",
  "description": "Learn Python programming from beginner to advanced",
  "price": 99.0,
  "vip_price": 79.0,
  "super_vip_price": 59.0,
  "img": "/uploads/courses/python.jpg",
  "image_url": "/uploads/courses/python.jpg",
  "sales": 1520,
  "status": "1",
  "category_id": 1,
  "interface_platform_id": 1,
  "cx_params": 1,
  "submit_params": 1,
  "addtime": "2024-01-01",
  "enable_image_search": true,
  "image_search_platform": "zykck",
  "category": {
    "id": 1,
    "name": "Programming Development"
  },
  "interface_platform": {
    "id": 1,
    "platform_name": "Learning Platform A"
  }
}
```

### 2.3 Get Public Category List

**GET** `/api/v1/public/categories`

No authentication required

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "name": "Programming Development",
      "description": "Programming development courses",
      "sort_order": 1,
      "is_active": true,
      "course_count": 50
    },
    {
      "id": 2,
      "name": "Design & Creation",
      "description": "Design and creative courses",
      "sort_order": 2,
      "is_active": true,
      "course_count": 30
    }
  ]
}
```

---

## 3. Order Related APIs

### 3.1 Create Order

**POST** `/api/v1/orders/`

Authentication required

**Request Parameters:**

```json
{
  "course_id": 1, // Required, course ID
  "membership_package_id": null, // Optional, membership package ID
  "order_type": "course", // Required, order type: course/membership
  "school_name": "Harvard University", // Optional, school name
  "student_account": "**********", // Optional, student account
  "student_password": "password123", // Optional, student password
  "customer_email": "<EMAIL>", // Optional, customer email (for order notifications)
  "notes": "Please process ASAP" // Optional, notes
}
```

**Response Example:**

```json
{
  "message": "Order created successfully",
  "data": {
    "id": 1001,
    "order_sn": "ORD20250115100001",
    "user_id": 1,
    "course_id": 1,
    "platform_id": 1,
    "course_name": "Python Programming Basics",
    "image_url": "/uploads/courses/python.jpg",
    "amount": 99.0,
    "final_amount": 79.0,
    "order_type": "course",
    "school_name": "Harvard University",
    "student_account": "**********",
    "status": "pending",
    "payment_status": false,
    "created_at": "2025-01-15T10:00:00Z"
  }
}
```

**Notification Details:**

- If `customer_email` is provided, system will automatically send email notifications when order is created
- Completion notification will also be sent to this email when order is completed
- Notifications include order information, course information, payment information, etc.

### 3.2 Get Order Details

**GET** `/api/v1/orders/{id}`

Authentication required

**Path Parameters:**

- `id`: Order ID

**Response Example:**

```json
{
  "id": 1001,
  "order_sn": "ORD20250115100001",
  "user_id": 1,
  "course_id": 1,
  "platform_id": 1,
  "course_name": "Python Programming Basics",
  "image_url": "/uploads/courses/python.jpg",
  "amount": 99.0,
  "discount_amount": 20.0,
  "final_amount": 79.0,
  "order_type": "course",
  "school_name": "Harvard University",
  "student_account": "**********",
  "status": "processing",
  "progress": "50%",
  "payment_status": true,
  "payment_type": "alipay",
  "payment_time": "2025-01-15T10:05:00Z",
  "platform_order_id": "PLAT123456",
  "is_submit": true,
  "submit_time": "2025-01-15T10:10:00Z",
  "user": {
    "id": 1,
    "username": "<EMAIL>"
  },
  "course": {
    "id": 1,
    "name": "Python Programming Basics"
  },
  "platform": {
    "id": 1,
    "platform_name": "Learning Platform A"
  }
}
```

### 3.3 Cancel Order

**PUT** `/api/v1/orders/{id}/cancel`

Authentication required

**Path Parameters:**

- `id`: Order ID

**Response Example:**

```json
{
  "message": "Order cancelled successfully"
}
```

### 3.4 Pay Order

**POST** `/api/v1/orders/{id}/pay`

Authentication required

**Path Parameters:**

- `id`: Order ID

**Request Parameters:**

```json
{
  "payment_type": "alipay" // Payment method: alipay/wechat/balance
}
```

**Response Example:**

```json
{
  "message": "Payment initiated",
  "payment_url": "https://payment.example.com/pay/123456",
  "payment_id": "PAY123456"
}
```

---

## 4. Payment Related APIs

### 4.1 Create Payment

**POST** `/api/v1/payments/`

Authentication required

**Request Parameters:**

```json
{
  "order_id": 1001, // Order ID
  "payment_method": "alipay", // Payment method
  "device": "pc", // Device type: pc/mobile
  "return_url": "https://example.com/payment/return" // Payment return URL
}
```

**Response Example:**

```json
{
  "message": "Payment created successfully",
  "data": {
    "payment_url": "https://openapi.alipay.com/gateway.do?...",
    "payment_id": "PAY20250115100001",
    "qr_code": "data:image/png;base64,...", // PC QR code
    "expires_at": "2025-01-15T10:30:00Z"
  }
}
```

### 4.2 Get Payment Details

**GET** `/api/v1/payments/{id}`

Authentication required

**Path Parameters:**

- `id`: Payment ID

**Response Example:**

```json
{
  "id": 1,
  "order_id": 1001,
  "user_id": 1,
  "transaction_id": "TRANS123456",
  "amount": 79.0,
  "currency": "USD",
  "method": "alipay",
  "status": "success",
  "created_at": "2025-01-15T10:00:00Z",
  "updated_at": "2025-01-15T10:05:00Z"
}
```

### 4.3 Query Payment Status

**GET** `/api/v1/payments/status/{order_sn}`

Authentication required

**Path Parameters:**

- `order_sn`: Order number

**Response Example:**

```json
{
  "order_sn": "ORD20250115100001",
  "payment_status": "success",
  "amount": 79.0,
  "payment_time": "2025-01-15T10:05:00Z",
  "transaction_id": "TRANS123456"
}
```

### 4.4 Get Payment Methods

**GET** `/api/v1/payments/methods`

Authentication required

**Response Example:**

```json
{
  "methods": [
    {
      "id": "alipay",
      "name": "Alipay",
      "icon": "/images/payment/alipay.png",
      "enabled": true
    },
    {
      "id": "wechat",
      "name": "WeChat Pay",
      "icon": "/images/payment/wechat.png",
      "enabled": true
    },
    {
      "id": "balance",
      "name": "Balance Payment",
      "icon": "/images/payment/balance.png",
      "enabled": true
    }
  ]
}
```

### 4.5 Get Device Types

**GET** `/api/v1/payments/devices`

Authentication required

**Response Example:**

```json
{
  "devices": ["pc", "mobile"]
}
```

### 4.6 Cancel Payment

**PUT** `/api/v1/payments/{id}/cancel`

Authentication required

**Path Parameters:**

- `id`: Payment ID

**Response Example:**

```json
{
  "message": "Payment cancelled successfully"
}
```

### 4.7 Payment Notification Callback (Payment Gateway Use)

**POST/GET** `/api/v1/payment/notify`

No authentication required (Payment gateway callback)

**Query Parameters:**

- `pid`: Merchant ID
- `trade_no`: Platform transaction number
- `out_trade_no`: Merchant order number
- `type`: Payment method
- `name`: Product name
- `money`: Amount
- `trade_status`: Transaction status
- `sign`: Signature
- `sign_type`: Signature type

**Response:**

```
success
```

### 4.8 Payment Return Page (Synchronous Notification)

**POST/GET** `/api/v1/payment/return`

No authentication required (Payment return)

**Query Parameters:**
Same as payment notification callback

**Response Example:**

```json
{
  "message": "Payment processed successfully",
  "data": {
    "trade_no": "TRADE123456",
    "out_trade_no": "ORD20250115100001",
    "status": "TRADE_SUCCESS"
  }
}
```

---

## 5. Personal Center APIs

### 5.1 Get User Profile

**GET** `/api/v1/users/profile`

Authentication required

**Response Example:**

```json
{
  "id": 1,
  "username": "<EMAIL>",
  "role": "user",
  "nickname": "John Smith",
  "faceimg": "/uploads/avatars/user1.jpg",
  "gender": "male",
  "location": "New York",
  "balance": 100.5,
  "price_ratio": 0.8,
  "order_count": 25,
  "query_count": 100,
  "vip_status": true,
  "vip_expire_at": "2025-12-31T23:59:59Z",
  "membership_type": "vip",
  "is_active": true,
  "last_login_at": "2025-01-15T09:00:00Z",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 5.2 Update User Profile

**PUT** `/api/v1/users/profile`

Authentication required

**Request Parameters:**

```json
{
  "nickname": "John Doe",
  "gender": "female",
  "location": "Los Angeles"
}
```

**Response Example:**

```json
{
  "message": "Profile updated successfully",
  "user": {
    "id": 1,
    "nickname": "John Doe",
    "gender": "female",
    "location": "Los Angeles"
  }
}
```

### 5.3 Change Password

**POST** `/api/v1/users/change-password`

Authentication required

**Request Parameters:**

```json
{
  "current_password": "oldpassword123",
  "new_password": "newpassword456"
}
```

**Response Example:**

```json
{
  "message": "Password changed successfully"
}
```

### 5.4 Get User Order List

**GET** `/api/v1/users/orders`

Authentication required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | int | No | Page number, default 1 |
| page_size | int | No | Items per page, default 20 |
| status | string | No | Order status filter |

**Response Example:**

```json
{
  "data": [
    {
      "id": 1001,
      "order_sn": "ORD20250115100001",
      "course_name": "Python Programming Basics",
      "amount": 99.0,
      "final_amount": 79.0,
      "status": "completed",
      "payment_status": true,
      "created_at": "2025-01-15T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 25
  }
}
```

### 5.5 Get User Payment Records

**GET** `/api/v1/users/payments`

Authentication required

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "order_id": 1001,
      "amount": 79.0,
      "method": "alipay",
      "status": "success",
      "transaction_id": "TRANS123456",
      "created_at": "2025-01-15T10:05:00Z"
    }
  ]
}
```

---

## 6. Membership Package APIs

### 6.1 Get Public Membership Package List

**GET** `/api/v1/public/membership-packages`

No authentication required

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "name": "Monthly VIP",
      "description": "Enjoy one month of VIP privileges",
      "price": 29.9,
      "duration_days": 30,
      "benefits": [
        "20% discount on courses",
        "Dedicated customer service",
        "Free resource downloads"
      ],
      "is_active": true
    },
    {
      "id": 2,
      "name": "Annual VIP",
      "description": "Enjoy one year of VIP privileges",
      "price": 299.0,
      "duration_days": 365,
      "benefits": [
        "30% discount on courses",
        "Dedicated customer service",
        "Free resource downloads",
        "Priority updates"
      ],
      "is_active": true
    }
  ]
}
```

### 6.2 Get Membership Package List (Authentication Required)

**GET** `/api/v1/membership/packages`

Authentication required

Response format same as above

### 6.3 Purchase Membership Package

**POST** `/api/v1/membership/purchase`

Authentication required

**Request Parameters:**

```json
{
  "package_id": 1 // Package ID
}
```

**Response Example:**

```json
{
  "message": "Membership package purchased successfully",
  "order": {
    "id": 1002,
    "order_sn": "ORD20250115100002",
    "package_id": 1,
    "amount": 29.9,
    "status": "pending_payment"
  }
}
```

### 6.4 Redeem Card Code

**POST** `/api/v1/membership/redeem`

Authentication required

**Request Parameters:**

```json
{
  "code": "CARD123456789" // Card code
}
```

**Response Example:**

```json
{
  "message": "Card code redeemed successfully",
  "membership": {
    "type": "vip",
    "expires_at": "2025-02-15T23:59:59Z"
  }
}
```

---

## 7. Promotion APIs

### 7.1 Validate Coupon

**GET** `/api/v1/public/coupons/{code}/validate`

No authentication required

**Path Parameters:**

- `code`: Coupon code

**Response Example:**

```json
{
  "valid": true,
  "coupon": {
    "id": 1,
    "code": "SAVE20",
    "name": "New User Coupon",
    "type": "percentage",
    "discount_value": 20,
    "min_order_amount": 50.0,
    "max_discount_amount": 30.0,
    "valid_from": "2025-01-01T00:00:00Z",
    "valid_until": "2025-02-01T23:59:59Z",
    "remaining_uses": 10
  }
}
```

### 7.2 Use Coupon

**POST** `/api/v1/promotions/coupons/use`

Authentication required

**Request Parameters:**

```json
{
  "code": "SAVE20", // Coupon code
  "order_id": 1001 // Order ID
}
```

**Response Example:**

```json
{
  "message": "Coupon applied successfully",
  "discount_amount": 20.0,
  "final_amount": 79.0
}
```

### 7.3 Get Active Flash Sales

**GET** `/api/v1/public/flash-sales`

No authentication required

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "name": "Flash Sale",
      "start_time": "2025-01-15T10:00:00Z",
      "end_time": "2025-01-15T12:00:00Z",
      "status": "active",
      "course": {
        "id": 1,
        "name": "Python Programming Basics",
        "original_price": 99.0,
        "flash_price": 49.0
      },
      "stock": 100,
      "sold_count": 35,
      "limit_per_user": 1
    }
  ]
}
```

### 7.4 Participate in Flash Sale

**POST** `/api/v1/promotions/flash-sales/{id}/participate`

Authentication required

**Path Parameters:**

- `id`: Flash sale ID

**Request Parameters:**

```json
{
  "quantity": 1 // Purchase quantity
}
```

**Response Example:**

```json
{
  "message": "Successfully participated in flash sale",
  "order": {
    "id": 1003,
    "order_sn": "ORD20250115100003",
    "amount": 49.0
  }
}
```

### 7.5 Validate Gift Card

**GET** `/api/v1/public/gift-cards/{code}/validate`

No authentication required

**Path Parameters:**

- `code`: Gift card code

**Response Example:**

```json
{
  "valid": true,
  "gift_card": {
    "id": 1,
    "code": "GIFT123456",
    "balance": 100.0,
    "status": "active",
    "expires_at": "2025-12-31T23:59:59Z"
  }
}
```

### 7.6 Use Gift Card

**POST** `/api/v1/promotions/gift-cards/use`

Authentication required

**Request Parameters:**

```json
{
  "code": "GIFT123456" // Gift card code
}
```

**Response Example:**

```json
{
  "message": "Gift card applied successfully",
  "added_balance": 100.0,
  "new_balance": 200.5
}
```

### 7.7 Generate Invitation Code

**GET** `/api/v1/promotions/invite/generate-code`

Authentication required

**Response Example:**

```json
{
  "invite_code": "INV123456",
  "invite_url": "https://example.com/register?invite=INV123456",
  "rewards": {
    "inviter_reward": 10.0,
    "invitee_reward": 5.0
  }
}
```

### 7.8 Register with Invitation Code

**POST** `/api/v1/promotions/invite/register`

Authentication required

**Request Parameters:**

```json
{
  "invite_code": "INV123456", // Invitation code
  "invitee_id": 2 // Invitee ID
}
```

**Response Example:**

```json
{
  "message": "Invitation registered successfully",
  "rewards": {
    "inviter_reward": 10.0,
    "invitee_reward": 5.0
  }
}
```

### 7.9 Get Invitation Statistics

**GET** `/api/v1/promotions/invite/stats`

Authentication required

**Response Example:**

```json
{
  "total_invites": 10,
  "successful_invites": 8,
  "total_rewards": 80.0,
  "pending_rewards": 20.0,
  "invite_code": "INV123456",
  "recent_invites": [
    {
      "invitee_name": "User A",
      "registered_at": "2025-01-10T10:00:00Z",
      "reward_amount": 10.0,
      "reward_status": "paid"
    }
  ]
}
```

---

## 8. Notification APIs

### 8.1 Get User Announcement List

**GET** `/api/v1/public/announcements`

No authentication required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| page | int | No | Page number, default 1 |
| page_size | int | No | Items per page, default 10 |
| type | string | No | Announcement type |

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "title": "System Maintenance Notice",
      "content": "System will undergo maintenance from 22:00-24:00 tonight",
      "type": "system",
      "priority": "high",
      "is_popup": false,
      "created_at": "2025-01-15T08:00:00Z",
      "view_count": 150
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 5
  }
}
```

### 8.2 View Announcement Details

**GET** `/api/v1/public/announcements/{id}`

No authentication required

**Path Parameters:**

- `id`: Announcement ID

**Response Example:**

```json
{
  "id": 1,
  "title": "System Maintenance Notice",
  "content": "Detailed maintenance content description...",
  "type": "system",
  "priority": "high",
  "attachments": [
    {
      "name": "maintenance.pdf",
      "url": "/uploads/attachments/maintenance.pdf"
    }
  ],
  "created_at": "2025-01-15T08:00:00Z",
  "updated_at": "2025-01-15T08:00:00Z",
  "view_count": 151
}
```

### 8.3 Get Popup Announcements

**GET** `/api/v1/public/announcements/popup`

No authentication required

**Response Example:**

```json
{
  "announcement": {
    "id": 2,
    "title": "Spring Festival Promotion",
    "content": "20% off on all courses",
    "image_url": "/uploads/announcements/spring_sale.jpg",
    "button_text": "View Now",
    "button_url": "/promotions",
    "show_once": true,
    "expires_at": "2025-02-01T23:59:59Z"
  }
}
```

### 8.4 Get Carousel Images

**GET** `/api/v1/public/banners`

No authentication required

**Query Parameters:**

- `position`: Position (default: home)

**Response Example:**

```json
{
  "data": [
    {
      "id": 1,
      "title": "New Course Launch",
      "image_url": "/uploads/banners/new_course.jpg",
      "link_url": "/courses/10",
      "link_type": "internal",
      "position": "home",
      "sort_order": 1,
      "click_count": 520
    },
    {
      "id": 2,
      "title": "Limited Time Offer",
      "image_url": "/uploads/banners/promotion.jpg",
      "link_url": "https://example.com/promo",
      "link_type": "external",
      "position": "home",
      "sort_order": 2,
      "click_count": 380
    }
  ]
}
```

### 8.5 Record Banner Click

**POST** `/api/v1/public/banners/{id}/click`

No authentication required

**Path Parameters:**

- `id`: Banner ID

**Response Example:**

```json
{
  "message": "Click recorded successfully"
}
```

---

## 9. Image Related APIs

### 9.1 Upload Image

**POST** `/api/v1/images/upload`

Authentication required

**Request Type:** `multipart/form-data`

**Form Parameters:**

- `image` or `file` or `avatar`: Image file (supports jpg, png, gif)

**Response Example:**

```json
{
  "url": "/uploads/images/2025/01/15/image_123456.jpg",
  "thumbnail": "/uploads/images/2025/01/15/image_123456_thumb.jpg",
  "size": 102400,
  "type": "image/jpeg",
  "width": 1920,
  "height": 1080
}
```

### 9.2 Upload Avatar

**POST** `/api/v1/images/upload-avatar`

Authentication required

**Request Type:** `multipart/form-data`

**Form Parameters:**

- `avatar`: Avatar file

**Response Example:**

```json
{
  "avatar_url": "/uploads/avatars/user_1_avatar.jpg",
  "message": "Avatar uploaded successfully"
}
```

### 9.3 Get Image Settings

**GET** `/api/v1/images/settings`

Authentication required

**Response Example:**

```json
{
  "max_size": 5242880, // 5MB
  "allowed_types": ["image/jpeg", "image/png", "image/gif"],
  "upload_path": "/uploads/images/",
  "thumbnail_sizes": {
    "small": "150x150",
    "medium": "300x300",
    "large": "800x800"
  }
}
```

### 9.4 Get Image Provider Status

**GET** `/api/v1/images/providers/status`

Authentication required

**Response Example:**

```json
{
  "providers": [
    {
      "name": "local",
      "status": "active",
      "used_space": **********, // 1GB
      "total_space": **********0 // 10GB
    },
    {
      "name": "oss",
      "status": "inactive",
      "error": "Configuration missing"
    }
  ]
}
```

---

## 10. Image Course Query APIs

### 10.1 Image Course Query (Admin Function)

**POST** `/api/v1/admin/platforms/image-search`

Admin authentication required

**Request Parameters:**

```json
{
  "platform": "zykck", // Platform code
  "school": "Some University", // School name (required for some platforms)
  "user": "Student ID", // Student account
  "pass": "password" // Student password
}
```

**Supported Platforms:**
| Platform Code | Platform Name |
|--------------|---------------|
| zykck | Learning Link Professional Course |
| xxkck | Learning Link Elective Course |
| long | Long Learning Link |
| sxkt | Mobile Learning Hall |
| mooc | Smart Vocational Education MOOC |
| spoc | Smart Vocational Education SPOC |
| zyk | Smart Vocational Education Resource Library |

**Response Example:**

```json
{
  "code": 1,
  "msg": "Query successful",
  "userName": "John Smith",
  "data": [
    {
      "name": "Python Programming Basics",
      "imageurl": "http://example.com/course.jpg",
      "state": "In Progress",
      "teachername": "Prof. Johnson",
      "id": "CRS123456",
      "sort": 1
    }
  ]
}
```

### 10.2 Get Supported Image Course Query Platforms

**GET** `/api/v1/admin/platforms/image-search/platforms`

Admin authentication required

**Response Example:**

```json
{
  "code": 1,
  "message": "Successfully retrieved platform list",
  "data": {
    "zykck": "Learning Link Professional Course",
    "xxkck": "Learning Link Elective Course",
    "long": "Long Learning Link",
    "sxkt": "Mobile Learning Hall",
    "mooc": "Smart Vocational Education MOOC",
    "spoc": "Smart Vocational Education SPOC",
    "zyk": "Smart Vocational Education Resource Library"
  }
}
```

### 10.3 Batch Image Course Query and Order Update

**POST** `/api/v1/admin/platforms/image-search/batch`

Admin authentication required

**Request Parameters:**

```json
{
  "platform": "zykck", // Platform code
  "order_ids": [1, 2, 3, 4, 5] // Order ID array
}
```

**Response Example:**

```json
{
  "code": 1,
  "message": "Batch image course query completed",
  "data": {
    "success_count": 3,
    "failure_count": 2,
    "failure_details": {
      "4": "Course ID mismatch",
      "5": "No matching course found"
    }
  }
}
```

### 10.4 Update Order Image URL

**PUT** `/api/v1/admin/platforms/orders/{order_id}/image`

Admin authentication required

**Path Parameters:**

- `order_id`: Order ID

**Request Parameters:**

```json
{
  "course_name": "Python Programming Basics",
  "course_id": "CRS123456",
  "image_url": "http://example.com/course_image.jpg"
}
```

**Response Example:**

```json
{
  "code": 1,
  "message": "Order image URL updated successfully"
}
```

### 10.5 Get Orders Without Images

**GET** `/api/v1/admin/platforms/orders/without-images`

Admin authentication required

**Query Parameters:**

- `limit`: Return limit (default 50, max 200)

**Response Example:**

```json
{
  "code": 1,
  "message": "Successfully retrieved",
  "data": [
    {
      "id": 1001,
      "course_name": "Python Programming Basics",
      "student_account": "**********",
      "created_at": "2025-01-15T10:00:00Z"
    }
  ]
}
```

---

## 11. System Configuration APIs

### 11.1 Get Customer Service Configuration

**GET** `/api/v1/public/customer-service`

No authentication required

**Response Example:**

```json
{
  "qq": "*********",
  "wechat": "CustomerService001",
  "email": "<EMAIL>",
  "phone": "************",
  "work_time": "Monday-Friday 9:00-18:00",
  "enabled": true
}
```

### 11.2 Health Check

**GET** `/health`

No authentication required

**Response Example:**

```json
{
  "status": "ok",
  "runtime_protection": true,
  "services": {
    "database": true,
    "redis": false,
    "queue": true
  }
}
```

---

## Error Codes

### Common Error Codes

| Error Code          | HTTP Status | Description                     |
| ------------------- | ----------- | ------------------------------- |
| INVALID_REQUEST     | 400         | Invalid request parameters      |
| UNAUTHORIZED        | 401         | Unauthorized access             |
| FORBIDDEN           | 403         | Access forbidden                |
| NOT_FOUND           | 404         | Resource not found              |
| METHOD_NOT_ALLOWED  | 405         | Request method not allowed      |
| CONFLICT            | 409         | Resource conflict               |
| RATE_LIMIT_EXCEEDED | 429         | Request rate limit exceeded     |
| INTERNAL_ERROR      | 500         | Internal server error           |
| SERVICE_UNAVAILABLE | 503         | Service temporarily unavailable |

### Business Error Codes

| Error Code           | Description                  |
| -------------------- | ---------------------------- |
| USER_NOT_FOUND       | User not found               |
| INVALID_CREDENTIALS  | Invalid username or password |
| ACCOUNT_DISABLED     | Account has been disabled    |
| COURSE_NOT_FOUND     | Course not found             |
| COURSE_UNAVAILABLE   | Course unavailable           |
| ORDER_NOT_FOUND      | Order not found              |
| ORDER_ALREADY_PAID   | Order already paid           |
| INSUFFICIENT_BALANCE | Insufficient balance         |
| PAYMENT_FAILED       | Payment failed               |
| COUPON_INVALID       | Invalid coupon               |
| COUPON_EXPIRED       | Coupon expired               |
| FLASH_SALE_ENDED     | Flash sale ended             |
| STOCK_INSUFFICIENT   | Insufficient stock           |

---

## Rate Limiting

### Global Rate Limits

- Maximum 600 requests per minute per IP
- Returns 429 status code when exceeded

### API-Level Rate Limits

| API Type          | Limit                           |
| ----------------- | ------------------------------- |
| Login APIs        | 10 requests per minute per IP   |
| Registration APIs | 10 requests per hour per IP     |
| Payment APIs      | 5 requests per minute per user  |
| Order Creation    | 10 requests per minute per user |
| Image Upload      | 5 requests per minute per user  |

### User-Level Rate Limits

- Authenticated users: 1000 requests per hour
- VIP users: 2000 requests per hour
- Super VIP users: 5000 requests per hour

---

## Security Mechanisms

### JWT Token

- Uses HS256 algorithm for signing
- Token validity: 24 hours
- Supports token refresh mechanism
- Contains user ID, username, role information

### Data Encryption

- Passwords stored using bcrypt encryption
- Sensitive data transmitted via HTTPS
- Payment information encrypted during transmission

### Protection Measures

- SQL injection protection
- XSS attack protection
- CSRF token validation
- IP whitelist support
- Request signature validation (payment APIs)

---

## WebSocket Real-time Communication

### Connection Address

```
ws://api.example.com/ws?token={jwt_token}
```

### Message Format

Send message:

```json
{
  "type": "subscribe",
  "channel": "order_status",
  "data": {
    "order_id": 1001
  }
}
```

Receive message:

```json
{
  "type": "order_update",
  "channel": "order_status",
  "data": {
    "order_id": 1001,
    "status": "processing",
    "progress": "75%"
  },
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### Supported Channels

- `order_status`: Order status updates
- `payment_status`: Payment status updates
- `notifications`: System notifications
- `announcements`: Announcement broadcasts

---

## Version Information

- **Current Version**: v1.0.0
- **Release Date**: 2025-01-15
- **API Version**: v1
- **Backward Compatible**: Yes

---

## Contact Support

- **Technical Support Email**: <EMAIL>
- **Developer Documentation**: https://docs.example.com/api
- **Status Monitor**: https://status.example.com
- **Issue Reporting**: https://github.com/example/api/issues
