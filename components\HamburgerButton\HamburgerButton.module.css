/* Hamburger Menu Styles */
.hamburgerBtn {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.hamburgerBtn:hover {
  background: var(--border-color);
}

.hamburgerLine {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

/* Animated hamburger when sidebar is open */
.hamburgerBtn.active .hamburgerLine:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburgerBtn.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.hamburgerBtn.active .hamburgerLine:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Responsive Styles */
@media screen and (max-width: 576px) {
  /* Show hamburger menu on mobile */
  .hamburgerBtn {
    display: flex;
  }
}

/* Tablet Responsive Styles */
@media screen and (min-width: 577px) and (max-width: 768px) {
  /* Show hamburger menu on tablet */
  .hamburgerBtn {
    display: flex;
  }
}

/* Desktop Responsive Styles */
@media screen and (min-width: 769px) {
  /* Hide hamburger on desktop */
  .hamburgerBtn {
    display: none;
  }
}
