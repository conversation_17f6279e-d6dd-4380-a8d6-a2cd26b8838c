'use client';

import React, { useState } from 'react'
import { Search, Lock } from 'lucide-react'
import { MobileSidebar, HamburgerButton } from '../../components'

const TestPage = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Mobile Sidebar */}
      <MobileSidebar
        isOpen={isSidebarOpen}
        onClose={closeSidebar}
        currentPath="/test"
      />

      {/* Header */}
      <header className="relative z-50 w-full">
        <nav className="flex items-center justify-between px-4 sm:px-8 py-4 max-w-7xl mx-auto" role="navigation" aria-label="Main navigation">
          {/* Mobile Hamburger Menu */}
          <HamburgerButton
            isOpen={isSidebarOpen}
            onClick={toggleSidebar}
          />

          <div className="text-xl sm:text-2xl font-bold text-black">
            <a href="/" className="hover:text-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg px-2 py-1" aria-label="CourseAssist homepage">
              CourseAssist.
            </a>
          </div>
          <div className="hidden md:flex items-center space-x-6 text-sm">
            <a href="/courses" className="text-gray-700 hover:text-black transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1">Courses</a>
            <a href="/categories" className="text-gray-700 hover:text-black transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1">Categories</a>
            <a href="/membership" className="text-gray-700 hover:text-black transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1">Membership</a>
            <button className="flex items-center space-x-1 text-gray-700 hover:text-black transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1" aria-label="Login to your account">
              <Lock size={14} aria-hidden="true" />
              <span>Login</span>
            </button>
          </div>
          {/* Keep original mobile menu button for desktop fallback */}
          <button className="md:hidden flex items-center justify-center w-8 h-8 text-gray-700 hover:text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded opacity-0 pointer-events-none" aria-label="Open mobile menu">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </nav>
      </header>

      {/* Clean Student Learning Design */}
      <div className="absolute top-8 right-4 lg:right-8 w-[300px] sm:w-[400px] lg:w-[480px] h-[400px] sm:h-[500px] lg:h-[600px] pointer-events-none z-20 hidden sm:block">
        
        {/* Main Hero Student - Larger and Central */}
        <div className="absolute top-8 sm:top-12 right-8 sm:right-12 w-40 h-40 sm:w-48 sm:h-48 lg:w-56 lg:h-56 group">
          <div className="relative w-full h-full">
            {/* Soft glow background */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-purple-200 rounded-full blur-2xl opacity-50" aria-hidden="true"></div>
            
            {/* Main student image */}
            <div className="relative w-full h-full rounded-full overflow-hidden shadow-2xl border-4 sm:border-6 border-white transform transition-all duration-500 motion-reduce:transition-none hover:scale-105 hover:shadow-3xl focus-within:ring-4 focus-within:ring-blue-200" role="img" aria-label="Student getting course assistance">
              <img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=500&h=500&fit=crop&crop=faces"
                alt="Student using course assistance service"
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 via-transparent to-transparent"></div>
            </div>

            {/* Code symbol - bigger */}
            <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl sm:rounded-3xl shadow-xl flex items-center justify-center transform rotate-12 hover:rotate-6 transition-transform duration-300 motion-reduce:transition-none" aria-hidden="true">
              <span className="text-white font-bold text-lg sm:text-xl">&lt;/&gt;</span>
            </div>

            {/* Achievement stars - bigger */}
            <div className="absolute -bottom-1 sm:-bottom-2 -left-2 sm:-left-4 flex space-x-1 sm:space-x-2" role="img" aria-label="5 star rating">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-xs sm:text-sm" aria-hidden="true">⭐</span>
              </div>
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-xs sm:text-sm" aria-hidden="true">⭐</span>
              </div>
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-xs sm:text-sm" aria-hidden="true">⭐</span>
              </div>
            </div>
          </div>
        </div>

        {/* Course Assistance Categories - Bigger and Better Spaced */}
        <div className="absolute top-60 sm:top-80 left-4 sm:left-8 flex space-x-4 sm:space-x-8" role="region" aria-label="Course assistance categories">
          {/* Programming Student */}
          <div className="relative group">
            <div className="w-20 h-20 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-2xl sm:rounded-3xl overflow-hidden shadow-xl border-2 sm:border-3 border-green-200 transform hover:scale-105 hover:-rotate-2 transition-all duration-400 motion-reduce:transition-none" role="img" aria-label="Student getting programming course assistance">
              <img
                src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=faces"
                alt="Programming student"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 to-transparent"></div>
            </div>
            <div className="absolute -bottom-2 sm:-bottom-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs sm:text-sm font-bold px-2 sm:px-3 py-1 sm:py-2 rounded-full shadow-lg">
              <span aria-hidden="true">💻</span> Programming
            </div>
          </div>

          {/* Business Student */}
          <div className="relative group">
            <div className="w-20 h-20 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-2xl sm:rounded-3xl overflow-hidden shadow-xl border-2 sm:border-3 border-purple-200 transform hover:scale-105 hover:rotate-2 transition-all duration-400 motion-reduce:transition-none" role="img" aria-label="Student completing business courses">
              <img
                src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=400&fit=crop&crop=faces"
                alt="Business student"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-purple-600/30 to-transparent"></div>
            </div>
            <div className="absolute -bottom-2 sm:-bottom-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white text-xs sm:text-sm font-bold px-2 sm:px-3 py-1 sm:py-2 rounded-full shadow-lg">
              <span aria-hidden="true">📊</span> Business
            </div>
          </div>

          {/* Design Student */}
          <div className="relative group">
            <div className="w-20 h-20 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-2xl sm:rounded-3xl overflow-hidden shadow-xl border-2 sm:border-3 border-pink-200 transform hover:scale-105 hover:-rotate-1 transition-all duration-400 motion-reduce:transition-none" role="img" aria-label="Student completing design courses">
              <img
                src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=400&h=400&fit=crop&crop=faces"
                alt="Design student"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-pink-600/30 to-transparent"></div>
            </div>
            <div className="absolute -bottom-2 sm:-bottom-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-pink-500 to-rose-600 text-white text-xs sm:text-sm font-bold px-2 sm:px-3 py-1 sm:py-2 rounded-full shadow-lg">
              <span aria-hidden="true">🎨</span> Design
            </div>
          </div>
        </div>



        {/* Simple Motivational Message */}
        <div className="absolute top-24 sm:top-32 left-4 sm:left-8 bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-blue-100 px-3 sm:px-4 py-2 sm:py-3 transform hover:scale-105 transition-transform duration-300 motion-reduce:transition-none" role="status" aria-live="polite">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full" aria-hidden="true"></div>
            <span className="text-xs sm:text-sm font-semibold text-gray-700">3,247 students learning now</span>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none" aria-hidden="true">
        {/* Light Purple Background - Left */}
        <div className="absolute top-16 left-0 w-[300px] sm:w-[420px] h-[300px] sm:h-[420px] bg-gradient-to-br from-purple-100 to-purple-200 rounded-full -translate-x-32 sm:-translate-x-52 opacity-60"></div>

        {/* Light Blue Background - Bottom Right */}
        <div className="absolute bottom-0 right-0 w-[280px] sm:w-[380px] h-[280px] sm:h-[380px] bg-gradient-to-br from-blue-100 to-blue-200 rounded-full translate-x-24 sm:translate-x-36 translate-y-24 sm:translate-y-36 opacity-60"></div>

        {/* Subtle Accent Elements */}
        <div className="absolute top-1/4 left-1/4 w-2 sm:w-3 h-2 sm:h-3 bg-yellow-400 rounded-full opacity-40"></div>
        <div className="absolute top-3/4 left-1/3 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-blue-400 rounded-full opacity-30"></div>
        <div className="absolute top-1/2 left-3/4 w-3 sm:w-4 h-3 sm:h-4 bg-red-400 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <main className="relative z-40 px-4 sm:px-8 pt-8 sm:pt-16 max-w-7xl mx-auto pb-32" role="main">
        
        {/* Hero Section with Search */}
        <div className="max-w-2xl mb-12">
          <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-8 sm:mb-12">
            <span className="text-black drop-shadow-sm">Complete</span><br />
            <span className="text-black drop-shadow-sm">your </span>
            <span className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent drop-shadow-lg font-extrabold">courses</span><br />
            <span className="text-black drop-shadow-sm">effortlessly.</span>
          </h1>

          {/* Search Bar */}
          <div className="mb-8 max-w-md group">
            <form role="search" aria-label="Course search">
              <div className="flex items-center bg-white rounded-xl shadow-lg border border-gray-200 px-4 py-4 transition-all duration-300 motion-reduce:transition-none group-hover:shadow-xl group-hover:scale-102 group-focus-within:ring-4 group-focus-within:ring-blue-100 group-focus-within:border-blue-300">
                <Search className="text-gray-400 mr-3 transition-colors duration-300 group-focus-within:text-blue-500" size={20} aria-hidden="true" />
                <input
                  type="search"
                  placeholder="Find course assistance services"
                  className="flex-1 outline-none text-gray-700 placeholder-gray-500 text-base transition-all duration-300 group-focus-within:placeholder-gray-400 focus:ring-0"
                  aria-label="Search for course assistance services"
                />
                <button type="submit" className="sr-only">Search</button>
              </div>
            </form>
          </div>
        </div>

        {/* Carousel/Activity Entry */}
        <section className="mb-12" role="region" aria-label="Featured promotions">
          <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-3xl p-6 sm:p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300 motion-reduce:transition-none">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl sm:text-3xl font-bold mb-2">New Course Launch!</h2>
                <p className="text-purple-100 mb-4">Get 50% off on premium programming courses this week</p>
                <button className="bg-white text-purple-600 font-semibold px-6 py-3 rounded-xl hover:bg-purple-50 transition-colors duration-300 shadow-lg">
                  Explore Courses →
                </button>
              </div>
              <div className="hidden sm:block text-6xl opacity-20">🚀</div>
            </div>
          </div>
        </section>

        {/* Activity Card Collection */}
        <section className="mb-12" role="region" aria-label="Platform activities">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Featured Activities</h2>
          
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {/* Flash Sales */}
            <div className="bg-gradient-to-br from-orange-50 to-red-50 border-2 border-orange-200 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">⚡</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Flash Sale</h3>
              <p className="text-gray-600 mb-3">Limited time offers on popular courses</p>
              <div className="bg-red-100 text-red-800 text-sm font-semibold px-3 py-1 rounded-full inline-block mb-4">
                Ends in: 2h 34m
              </div>
              <button className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold py-3 rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300">
                Buy Now
              </button>
            </div>

            {/* Membership Packages */}
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">👑</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">VIP Membership</h3>
              <p className="text-gray-600 mb-3">Unlock premium features & exclusive discounts</p>
              <div className="bg-purple-100 text-purple-800 text-sm font-semibold px-3 py-1 rounded-full inline-block mb-4">
                Starting at $29.99/month
              </div>
              <button className="w-full bg-gradient-to-r from-purple-500 to-indigo-500 text-white font-semibold py-3 rounded-xl hover:from-purple-600 hover:to-indigo-600 transition-all duration-300">
                Upgrade Now
              </button>
            </div>

            {/* Coupon/Gift Card Entry */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 sm:col-span-2 lg:col-span-1">
              <div className="text-3xl mb-4">🎁</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Redeem Code</h3>
              <p className="text-gray-600 mb-4">Enter your coupon or gift card code</p>
              <input 
                type="text" 
                placeholder="Enter code" 
                className="w-full border border-green-300 rounded-lg px-4 py-3 mb-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300">
                Redeem
              </button>
            </div>
          </div>
        </section>

        {/* Recommended Courses */}
        <section className="mb-12" role="region" aria-label="Recommended courses">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-800">Recommended Courses</h2>
            <a href="/courses" className="text-blue-600 hover:text-blue-800 font-medium text-sm">View All →</a>
          </div>
          
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {/* Course 1 */}
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-2">
              <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-6xl text-white">
                💻
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">Python Programming Basics</h3>
                <p className="text-gray-600 text-sm mb-4">Complete your Python courses with expert assistance</p>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm text-gray-600 ml-1">4.8 (1,234)</span>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold text-blue-600">$99.00</span>
                    <span className="text-sm text-gray-400 line-through ml-2">$149.00</span>
                  </div>
                </div>
                <button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                  Get Assistance
                </button>
              </div>
            </div>

            {/* Course 2 */}
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-2">
              <div className="h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center text-6xl text-white">
                📊
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">Business Analytics</h3>
                <p className="text-gray-600 text-sm mb-4">Master business analytics with guided assistance</p>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm text-gray-600 ml-1">4.9 (856)</span>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold text-green-600">$129.00</span>
                    <span className="text-sm text-gray-400 line-through ml-2">$199.00</span>
                  </div>
                </div>
                <button className="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white font-semibold py-3 rounded-xl hover:from-green-600 hover:to-blue-700 transition-all duration-300">
                  Get Assistance
                </button>
              </div>
            </div>

            {/* Course 3 */}
            <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-2">
              <div className="h-48 bg-gradient-to-br from-pink-400 to-red-500 flex items-center justify-center text-6xl text-white">
                🎨
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">UI/UX Design</h3>
                <p className="text-gray-600 text-sm mb-4">Complete design courses with professional guidance</p>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm text-gray-600 ml-1">4.7 (642)</span>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold text-pink-600">$89.00</span>
                    <span className="text-sm text-gray-400 line-through ml-2">$129.00</span>
                  </div>
                </div>
                <button className="w-full bg-gradient-to-r from-pink-500 to-red-600 text-white font-semibold py-3 rounded-xl hover:from-pink-600 hover:to-red-700 transition-all duration-300">
                  Get Assistance
                </button>
              </div>
            </div>
          </div>
        </section>

      </main>

      {/* Bottom Navigation - Mobile App Style */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-gray-200 z-50 shadow-2xl" role="navigation" aria-label="Main navigation">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex items-center justify-around">
            
            {/* Home */}
            <button className="flex flex-col items-center py-2 px-3 rounded-xl bg-blue-50 text-blue-600 transition-all duration-300">
              <div className="text-xl mb-1">🏠</div>
              <span className="text-xs font-semibold">Home</span>
            </button>

            {/* Explore/Categories */}
            <button className="flex flex-col items-center py-2 px-3 rounded-xl text-gray-500 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300">
              <div className="text-xl mb-1">🔍</div>
              <span className="text-xs font-medium">Explore</span>
            </button>

            {/* Orders */}
            <button className="flex flex-col items-center py-2 px-3 rounded-xl text-gray-500 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300">
              <div className="text-xl mb-1">📋</div>
              <span className="text-xs font-medium">Orders</span>
            </button>

            {/* Profile */}
            <button className="flex flex-col items-center py-2 px-3 rounded-xl text-gray-500 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300">
              <div className="text-xl mb-1">👤</div>
              <span className="text-xs font-medium">Profile</span>
            </button>

          </div>
        </div>
      </nav>

      {/* Bottom Spacer for Fixed Navigation */}
      <div className="h-20"></div>
    </div>
  )
}

export default TestPage
