/* Mobile Sidebar Styles */
.sidebarOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 998;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebarOverlay.show {
  opacity: 1;
}

.mobileSidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--white);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

.sidebarOpen {
  transform: translateX(0);
}

.sidebarHeader {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebarTitle {
  font-size: 20px;
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.closeSidebarBtn {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeSidebarBtn:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.sidebarNav {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebarNavItem {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s ease;
  border-radius: 0;
}

.sidebarNavItem:hover {
  background: var(--border-color);
  color: var(--primary-blue);
}

.sidebarNavItem.active {
  background: var(--primary-gradient);
  color: var(--white);
}

.sidebarNavItem.active:hover {
  background: var(--primary-gradient);
  color: var(--white);
}

.sidebarNavIcon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.sidebarNavLabel {
  font-size: 16px;
  font-weight: var(--font-medium);
}

.sidebarFooter {
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.logoutBtn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px;
  background: none;
  border: none;
  color: var(--error-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  width: 100%;
  text-align: left;
}

.logoutBtn:hover {
  background: var(--error-bg);
}

/* Mobile Responsive Styles */
@media screen and (max-width: 576px) {
  /* Show sidebar overlay on mobile when sidebar is open */
  .sidebarOverlay {
    display: block;
  }
}

/* Tablet Responsive Styles */
@media screen and (min-width: 577px) and (max-width: 768px) {
  /* Show sidebar overlay on tablet when sidebar is open */
  .sidebarOverlay {
    display: block;
  }
}

/* Desktop Responsive Styles */
@media screen and (min-width: 769px) {
  /* Hide mobile sidebar on desktop */
  .mobileSidebar {
    display: none;
  }

  .sidebarOverlay {
    display: none;
  }
}
